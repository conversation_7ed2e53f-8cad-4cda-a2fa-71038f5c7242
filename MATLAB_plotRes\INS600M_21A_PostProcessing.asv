%*************20240815*****post test******
clc;
close all;
clear;

% 1. 读取数据（区分表格和矩阵）
try
    % 尝试用readtable读取为表格
    ResData = readtable('../POST_Raw_Version_LiGong/data/inavresult-3.txt', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
    ObsData = readtable('../POST_Raw_Version_LiGong/data/inavdata-3.csv', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
    StandData = readtable('../POST_Raw_Version_LiGong/data/inavstandard-3.csv', ...
        'HeaderLines', 1, 'ReadVariableNames', false);
    is_table = true;  % 标记为表格类型
catch
    % 备用方案：用read_numeric读取为矩阵（非表格）
    ResData = read_numeric('../POST_Raw_Version_LiGong/data/inavresult-3.txt');
    ObsData = read_numeric('../POST_Raw_Version_LiGong/data/inavdata-3.csv');
    StandData = read_numeric('../POST_Raw_Version_LiGong/data/inavstandard-3.csv');
    is_table = false;  % 标记为矩阵类型
end

% 2. 仅当是表格时才转换为数组（修正核心！）
if is_table
    ResData = table2array(ResData);
    ObsData = table2array(ObsData);
    StandData = table2array(StandData);
end

% 3. 数据有效性校验（增强版）
if size(ResData,1) < 1
    error('ResData为空，请检查数据文件路径是否正确');
end
if size(ResData,2) < 4
    error('ResData列数不足，需要至少4列（时间、纬度、经度、高度）');
end
if any(isnan(ResData(:,2:4)))  % 检查纬度、经度、高度是否有NaN
    warning('ResData包含无效值，已过滤');
    ResData = ResData(~any(isnan(ResData(:,2:4)),2), :);
end
if size(ResData,1) < 1
    error('过滤后ResData为空，无法继续');
end

% % 检查GPSdat数据
% if size(GPSdat,2) < 10
%     warning('GPSdat列数不足，可能影响后续分析');
% end

% 常量定义
DEG2RAD = pi/180;
R = 6378137;  % 地球半径(米)

% 提取GPS数据（间隔>10的点）
Len = size(ObsData,1);
GPSdat = ObsData(1,:);
if Len >= 2
    GPStow = ObsData(:,21);
    for k = 2:Len
        if GPStow(k) - GPStow(k-1) > 10
            GPSdat = [GPSdat; ObsData(k,:)];
        end
    end
end


%       g_timetamp,1
% 		pNAV_Data_Out->latitude,2
% 		pNAV_Data_Out->longitude,3
% 		pNAV_Data_Out->altitude,4
% 		pNAV_Data_Out->ve,5
% 		pNAV_Data_Out->vn,6
% 		pNAV_Data_Out->vu,7
% 		pNAV_Data_Out->pitch,8
% 		pNAV_Data_Out->roll,9
% 		pNAV_Data_Out->heading,10
% 		pNAV_Data_Out->Nav_Status,11
% 		pNAV_Data_Out->heading_gps,12
% 		pNAV_Data_Out->rtkStatus,13
% 		pNAV_Data_Out->delay,14
% 		pNAV_Data_Out->pdop);15


% pNAV_Data_Full->IMU.gyro_use[0],
% 		pNAV_Data_Full->IMU.gyro_use[1],
% 		pNAV_Data_Full->IMU.gyro_use[2],
% 		pNAV_Data_Full->IMU.acc[0],
% 		pNAV_Data_Full->IMU.acc[1],
% 		pNAV_Data_Full->IMU.acc[2],
% 		pNAV_Data_Full->IMU.temp_use,
% 		pNAV_Data_Full->GPS.Lat,
% 		pNAV_Data_Full->GPS.Lon,
% 		pNAV_Data_Full->GPS.Altitude,
% 		pNAV_Data_Full->GPS.ve,
% 		pNAV_Data_Full->GPS.vn,
% 		pNAV_Data_Full->GPS.vu,
% 		pNAV_Data_Full->GPS.Heading_cor,
% 		pNAV_Data_Full->GPS.Position_Status,
% 		pNAV_Data_Full->GPS.rtkStatus,
% 		pNAV_Data_Full->GPS.Sate_Num,
% 	    pNAV_Data_Full->GPS.delay_pps,
% 		pNAV_Data_Full->GPS.gpsweek,
% 		pNAV_Data_Full->GPS.gpssecond,
% 		pNAV_Data_Full->GPS.gpssecond982,
% 		pNAV_Data_Full->GPS.headingStatus,
% 		pNAV_Data_Full->GPS.pdop,
% 		pNAV_Data_Full->GPS.trackTrue,
% 		pNAV_Data_Full->ODS.counter,
% 		pNAV_Data_Full->ODS.WheelSpeed_Front_Left,
% 		pNAV_Data_Full->ODS.WheelSpeed_Front_Right,
% 		pNAV_Data_Full->ODS.WheelSpeed_Back_Left,
% 		pNAV_Data_Full->ODS.WheelSpeed_Back_Right,
% 		pNAV_Data_Full->ODS.WheelSteer,
% 		pNAV_Data_Full->ODS.OdoPulse_1,
% 		pNAV_Data_Full->ODS.OdoPulse_2,
% 		pNAV_Data_Full->ODS.Gear,
% 		pNAV_Data_Full->GPS.gps_up_flag,
% 		pNAV_Data_Full->GPS.NO_RTK_heading_flag,
% 		pNAV_Data_Full->ODS.ods_caculat_flag,
% 		pNAV_Data_Full->Nav_Status,
% 		pNAV_Data_Full->Nav_Standard_flag,
% 		fabs(pNAV_Data_Full->EARTH.gn[2]));//0:标定未完成  1：标定完成
%--------------------------------------------------------------------------
% 后续图表代码（经纬度高、东北天等）保持不变
%--------------------------------------------------------------------------
% 1. 经纬度高图表
figure('Name','经纬度高');
subplot(2,1,1); hold on; grid on;
plot(ResData(:,3), ResData(:,2), '.-r', 'DisplayName','算法结果');
plot(GPSdat(:,9), GPSdat(:,8), '.-b', 'DisplayName','GNSS轨迹');
xlabel('经度(°)'); ylabel('纬度(°)'); title('经纬度轨迹'); legend;

subplot(2,1,2); hold on; grid on;
plot(ResData(:,1)*0.001, ResData(:,4), '.-r', 'DisplayName','算法高度');
plot(GPSdat(:,20)*0.001, GPSdat(:,10), '.-b', 'DisplayName','GNSS高度');
xlabel('时间(s)'); ylabel('高度(m)'); title('高度变化'); legend;

% 2. 东北天xyz变化（调用修正后的llh2enu）
lat0 = ResData(1,2) * DEG2RAD;
lon0 = ResData(1,3) * DEG2RAD;
alt0 = ResData(1,4);

gpslat0 = GPSdat(1,8) * DEG2RAD;
gpslon0 = GPSdat(1,9) * DEG2RAD;
gpsalt0 = GPSdat(1,10);
[E_res, N_res, U_res] = llh2enu(ResData(:,2), ResData(:,3), ResData(:,4), lat0, lon0, alt0, R, DEG2RAD);
[E_gps, N_gps, U_gps] = llh2enu(GPSdat(:,8), GPSdat(:,9), GPSdat(:,10), gpslat0, gpslon0, gpsalt0, R, DEG2RAD);

figure('Name','东北天xyz变化对比');
% 东向对比图
subplot(3,1,1); hold on; grid on;
plot(ResData(:,1)*0.001, E_res, '.-r', 'LineWidth',1, 'DisplayName','算法东向');
plot(GPSdat(:,20)*0.001, E_gps, '.-b', 'LineWidth',1, 'DisplayName','GPS东向');
xlabel('时间(s)'); ylabel('东向(m)'); title('东向位置对比');
legend('Location','best');  % 显示图例

% 北向对比图
subplot(3,1,2); hold on; grid on;
plot(ResData(:,1)*0.001, N_res, '.-r', 'LineWidth',1, 'DisplayName','算法北向');
plot(GPSdat(:,20)*0.001, N_gps, '.-b', 'LineWidth',1, 'DisplayName','GPS北向');
xlabel('时间(s)'); ylabel('北向(m)'); title('北向位置对比');
legend('Location','best');  % 显示图例

% 天向对比图
subplot(3,1,3); hold on; grid on;
plot(ResData(:,1)*0.001, U_res, '.-r', 'LineWidth',1, 'DisplayName','算法天向');
plot(GPSdat(:,20)*0.001, U_gps, '.-b', 'LineWidth',1, 'DisplayName','GPS天向');
xlabel('时间(s)'); ylabel('天向(m)'); title('天向位置对比');
legend('Location','best');  % 显示图例


% 其他图表代码...


%--------------------------------------------------------------------------
% llh2enu函数（保持之前的修正）
%--------------------------------------------------------------------------
function [E, N, U] = llh2enu(lat_deg, lon_deg, h, lat0_rad, lon0_rad, h0, R, DEG2RAD)
    lat_rad = lat_deg(:) * DEG2RAD;  % 转为列向量+弧度
    lon_rad = lon_deg(:) * DEG2RAD;
    dLat = lat_rad - lat0_rad;
    dLon = lon_rad - lon0_rad;
    E = (R + h(:)) .* cos(lat0_rad) .* dLon;
    N = (R + h(:)) .* dLat;
    U = h(:) - h0;
end

%--------------------------------------------------------------------------
% 辅助函数：读取纯数值文件
%--------------------------------------------------------------------------
function data = read_numeric(filename)
    fid = fopen(filename, 'r');
    if fid == -1
        error('无法打开文件：%s', filename);
    end
    data = [];
    while ~feof(fid)
        line = fgetl(fid);
        line = strrep(line, ',', ' ');  % 逗号转空格
        nums = sscanf(line, '%f ');     % 提取数值
        if ~isempty(nums)
            data = [data; nums];
        end
    end
    fclose(fid);
end
